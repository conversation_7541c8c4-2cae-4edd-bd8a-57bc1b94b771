<!-- Product Form Header -->
<div class="bg-white border-b border-gray-200 px-6 py-4">
	<div class="flex flex-col md:flex-row md:items-center justify-between">
		<div class="flex items-center">
			<a href="{{ back_url }}" data-readdy="true" class="mr-3 text-gray-500 hover:text-primary">
				<div class="w-8 h-8 flex items-center justify-center">
					<i class="ri-arrow-left-line ri-lg"></i>
				</div>
			</a>
			<div>
				<h1 class="text-2xl font-bold text-gray-800">{{ heading_title }}</h1>
			</div>
		</div>
		<div class="flex items-center space-x-3 mt-4 md:mt-0">
			<a href="{{ back }}" class="px-4 py-2 border border-gray-300 text-gray-700 rounded-button hover:bg-gray-50 transition-colors whitespace-nowrap !rounded-button">
				<span>Отказ</span>
			</a>
			<button type="submit" form="product-form" class="px-6 py-2 bg-primary text-white rounded-button hover:bg-primary/90 transition-colors whitespace-nowrap flex items-center !rounded-button">
				<div class="w-5 h-5 flex items-center justify-center mr-2">
					<i class="ri-save-line"></i>
				</div>
				<span>Запази</span>
			</button>
		</div>
	</div>
</div>
<!-- Main Content Area -->
<main class="flex-1 overflow-y-auto bg-gray-50 p-6">
	<form id="product-form" class="space-y-6">
		<input type="hidden" name="product_id" value="{{ product_id ?? 0 }}">
		<div class="max-w-7xl">
			<div class="bg-white rounded-lg shadow-sm overflow-hidden mb-6">
				<div class="border-b border-gray-200">
					<div class="flex overflow-x-auto">
						<button data-tab="tab-basic-info" class="tab-button active px-6 py-4 text-sm font-medium whitespace-nowrap">Основна информация</button>
						<button data-tab="tab-images" class="tab-button px-6 py-4 text-sm font-medium text-gray-500 whitespace-nowrap">Изображения</button>
						<button data-tab="tab-description" class="tab-button px-6 py-4 text-sm font-medium text-gray-500 whitespace-nowrap">Описание</button>
						<button data-tab="tab-specifications" class="tab-button px-6 py-4 text-sm font-medium text-gray-500 whitespace-nowrap">Атрибути</button>
						<button data-tab="tab-related" class="tab-button px-6 py-4 text-sm font-medium text-gray-500 whitespace-nowrap">Свързани продукти</button>
						<button data-tab="tab-seo" class="tab-button px-6 py-4 text-sm font-medium text-gray-500 whitespace-nowrap">SEO</button>
					</div>
				</div>
				<!-- Tab Content -->
				<div
					class="p-6">
					<!-- Basic Info Tab -->
					<div id="tab-basic-info" class="tab-content">
						<div class="grid grid-cols-1 md:grid-cols-2 gap-6">
							<div>
								<label class="block text-sm font-medium text-gray-700 mb-1">Име на продукта
									<span class="text-red-500">*</span>
								</label>
								<input type="text" name="product_description[{{ active_language_id }}][name]" class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary text-sm" placeholder="Въведете име на продукта" value="{{ product_description[active_language_id].name }}">
							</div>
							<div>
								<label class="block text-sm font-medium text-gray-700 mb-1">Код на продукта
									<span class="text-red-500">*</span>
								</label>
								<input type="text" name="model" class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary text-sm" placeholder="Въведете код на продукта" value="{{ model }}">
							</div>
							<div>
								<label class="block text-sm font-medium text-gray-700 mb-1">Категория
									<span class="text-red-500">*</span>
								</label>
								<input type="text" name="category_name" id="input-category" class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary text-sm" placeholder="Започнете да пишете за търсене на категория..." value="">
								<div id="product-category-autocomplete" class="mt-2 space-y-2">
									<!-- Autocomplete suggestions will be populated here -->
								</div>
								<div id="product-category" class="mt-2 space-y-2">
									{% for product_category_id in product_category %}
									<div id="product-category{{ product_category_id }}" class="flex items-center justify-between p-2 bg-gray-100 rounded">
										<span class="text-sm">{{ categories[product_category_id].name }}</span>
										<button type="button" class="text-gray-400 hover:text-red-500">
											<i class="ri-close-line"></i>
										</button>
										<input type="hidden" name="product_category[]" value="{{ product_category_id }}">
									</div>
									{% endfor %}
								</div>
							</div>
							<div>
								<label class="block text-sm font-medium text-gray-700 mb-1">Марка</label>
								<input type="text" name="manufacturer_name" id="input-manufacturer" class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary text-sm" placeholder="Започнете да пишете за търсене на марка..." value="{{ manufacturer_name }}">
								<div id="product-manufacturer-autocomplete" class="autocomplete-suggestions-container mt-1 relative" style="z-index: 1000;">
									<!-- Autocomplete suggestions will be populated here -->
								</div>
								<div id="selected-manufacturer-container" class="mt-2">
									{% if manufacturer_id and manufacturer_name %}
									<div class="selected-brand-badge flex items-center justify-between bg-primary/10 text-primary px-3 py-1.5 rounded-md text-sm my-1">
										<span>{{ manufacturer_name }}</span>
										<button type="button" class="remove-selected-manufacturer ml-2 text-red-500 hover:text-red-700" data-id="{{ manufacturer_id }}">
											<i class="ri-close-line"></i>
										</button>
									</div>
									{% endif %}
								</div>
								<input type="hidden" name="manufacturer_id" value="{{ manufacturer_id }}">
							</div>
							<div>
								<label class="block text-sm font-medium text-gray-700 mb-1">Основна цена (лв.)
									<span class="text-red-500">*</span>
								</label>
								<div class="relative">
									<input type="number" name="price" step="0.01" class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary text-sm" placeholder="0.00" value="{{ price }}">
									<div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
										<span class="text-gray-500 text-sm">лв.</span>
									</div>
								</div>
							</div>
							<div>
								<label class="block text-sm font-medium text-gray-700 mb-1">Наличност
									<span class="text-red-500">*</span>
								</label>
								<input type="number" name="quantity" class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary text-sm" placeholder="Въведете количество" value="{{ quantity }}">
							</div>
							<div>
								<div class="flex justify-between items-center mb-2">
									<label class="block text-sm font-medium text-gray-700">Промоционални цени</label>
									<button type="button" id="add-special-price" class="px-3 py-1.5 bg-primary text-white rounded-button hover:bg-primary/90 transition-colors text-sm flex items-center !rounded-button">
										<div class="w-4 h-4 flex items-center justify-center mr-1">
											<i class="ri-add-line"></i>
										</div>
										<span>Добави промоция</span>
									</button>
								</div>
								<div id="special-prices-container" class="space-y-3">
									{% for special in product_special %}
									<div class="special-price-item grid grid-cols-1 md:grid-cols-4 gap-3 p-3 border border-gray-200 rounded-lg">
										<div>
											<label class="block text-xs font-medium text-gray-600 mb-1">Цена (лв.)</label>
											<div class="relative">
												<input type="number" name="product_special[{{ loop.index0 }}][price]" step="0.01" class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary text-sm" placeholder="0.00" value="{{ special.price|number_format(2, '.', '') }}">
												<div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
													<span class="text-gray-500 text-xs">лв.</span>
												</div>
											</div>
										</div>
										<div>
											<label class="block text-xs font-medium text-gray-600 mb-1">От дата</label>
											<input type="date" name="product_special[{{ loop.index0 }}][date_start]" class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary text-sm" value="{{ special.date_start }}">
										</div>
										<div>
											<label class="block text-xs font-medium text-gray-600 mb-1">До дата</label>
											<input type="date" name="product_special[{{ loop.index0 }}][date_end]" class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary text-sm" value="{{ special.date_end }}">
										</div>
										<div class="flex items-end">
											<button type="button" class="remove-special-price w-8 h-8 flex items-center justify-center bg-red-50 text-red-500 border border-red-200 rounded hover:bg-red-100 transition-colors">
												<i class="ri-delete-bin-line text-sm"></i>
											</button>
										</div>
									</div>
									{% else %}
									<div id="no-special-prices" class="text-gray-500 text-sm italic text-center py-3 border border-dashed border-gray-300 rounded-lg">
										Няма добавени промоционални цени. Използвайте бутона "Добави промоция" за да добавите нова промоционална цена.
									</div>
									{% endfor %}
								</div>
							</div>
							
							<div>
								<label class="block text-sm font-medium text-gray-700 mb-1">Статус</label>
								<div class="flex items-center space-x-2 mt-2">
									<label class="toggle-switch">
										<input type="checkbox" name="status" value="1" {% if status %}checked{% endif %}>
										<span class="toggle-slider"></span>
									</label>
									<span class="text-sm text-gray-700">Активен</span>
								</div>
							</div>
						</div>
					</div>
					<!-- Images Tab (Hidden by default) -->
					<div id="tab-images" class="tab-content hidden">
						<div class="mb-6">
							<label class="block text-sm font-medium text-gray-700 mb-2">Изображения на продукта</label>
							<div class="image-upload-area rounded-lg p-8 flex flex-col items-center justify-center cursor-pointer">
								<div class="w-16 h-16 flex items-center justify-center text-gray-400 mb-4">
									<i class="ri-image-add-line ri-2x"></i>
								</div>
								<p class="text-sm text-gray-500 mb-1">Плъзнете и пуснете изображения тук</p>
								<p class="text-xs text-gray-400">или</p>
								<label class="mt-4 px-4 py-2 bg-primary text-white rounded-button hover:bg-primary/90 transition-colors cursor-pointer !rounded-button">
									<span>Изберете файлове</span>
									<input type="file" name="image" multiple class="hidden" accept="image/*">
								</label>
								<p class="text-xs text-gray-400 mt-4">Поддържани формати: JPG, PNG, GIF. Максимален размер: 5MB</p>
							</div>
						</div>
						<div class="mt-8">
							<h3 class="text-sm font-medium text-gray-700 mb-4">Качени изображения</h3>
							<div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4">
								<div class="relative group">
									<div class="aspect-square rounded-lg overflow-hidden border border-gray-200">
										<img src="{{ thumb }}" alt="Product image" class="w-full h-full object-cover">
									</div>
									<div class="absolute inset-0 bg-black bg-opacity-50 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center gap-2">
										<button class="p-2 bg-white rounded-full text-gray-700 hover:text-primary">
											<div class="w-5 h-5 flex items-center justify-center">
												<i class="ri-eye-line"></i>
											</div>
										</button>
										<button class="p-2 bg-white rounded-full text-gray-700 hover:text-primary">
											<div class="w-5 h-5 flex items-center justify-center">
												<i class="ri-image-edit-line"></i>
											</div>
										</button>
										<button class="p-2 bg-white rounded-full text-red-500 hover:text-red-600">
											<div class="w-5 h-5 flex items-center justify-center">
												<i class="ri-delete-bin-line"></i>
											</div>
										</button>
									</div>
									<div class="absolute top-2 left-2">
										<div class="px-2 py-1 bg-primary text-white text-xs rounded-full">Основна</div>
									</div>
								</div>
								<div class="border border-dashed border-gray-300 rounded-lg p-4 flex items-center justify-center cursor-pointer hover:border-primary hover:bg-primary/5 transition-colors" onclick="document.getElementById('additional-image-upload').click()">
									<div class="w-8 h-8 flex items-center justify-center text-gray-400">
										<i class="ri-add-line ri-lg"></i>
									</div>
									<input type="file" name="product_image[][image]" id="additional-image-upload" class="hidden" accept="image/*" multiple>
								</div>
							</div>
						</div>
					</div>
					<!-- Description Tab (Hidden by default) -->
					<div id="tab-description" class="tab-content hidden">
						<div class="mt-6">
							<label class="block text-sm font-medium text-gray-700 mb-2">Пълно описание</label>
							<div class="border border-gray-300 rounded overflow-hidden">
								<div class="bg-gray-50 border-b border-gray-300 p-2 flex space-x-2">
									<button class="p-1 hover:bg-gray-200 rounded">
										<div class="w-6 h-6 flex items-center justify-center">
											<i class="ri-bold"></i>
										</div>
									</button>
									<button class="p-1 hover:bg-gray-200 rounded">
										<div class="w-6 h-6 flex items-center justify-center">
											<i class="ri-italic"></i>
										</div>
									</button>
									<button class="p-1 hover:bg-gray-200 rounded">
										<div class="w-6 h-6 flex items-center justify-center">
											<i class="ri-underline"></i>
										</div>
									</button>
									<button class="p-1 hover:bg-gray-200 rounded">
										<div class="w-6 h-6 flex items-center justify-center">
											<i class="ri-list-unordered"></i>
										</div>
									</button>
									<button class="p-1 hover:bg-gray-200 rounded">
										<div class="w-6 h-6 flex items-center justify-center">
											<i class="ri-list-ordered"></i>
										</div>
									</button>
									<button class="p-1 hover:bg-gray-200 rounded">
										<div class="w-6 h-6 flex items-center justify-center">
											<i class="ri-link"></i>
										</div>
									</button>
									<button class="p-1 hover:bg-gray-200 rounded">
										<div class="w-6 h-6 flex items-center justify-center">
											<i class="ri-image-line"></i>
										</div>
									</button>
								</div>
								<textarea name="product_description[{{ active_language_id }}][description]" rows="10" class="w-full px-3 py-2 border-none focus:outline-none focus:ring-0 text-sm" placeholder="Въведете пълно описание на продукта">{{ product_description[active_language_id].description }}</textarea>
							</div>
						</div>
					</div>
					<!-- Specifications Tab (Hidden by default) -->
					<div id="tab-specifications" class="tab-content hidden">
						<!-- Атрибути на продукта -->
						<div class="mb-8">
							<div class="mb-4 flex justify-between items-center">
								<h3 class="text-sm font-medium text-gray-700">Атрибути на продукта</h3>
								<button id="add-attribute" type="button" class="px-3 py-1.5 bg-primary text-white rounded-button hover:bg-primary/90 transition-colors text-sm flex items-center !rounded-button">
									<div class="w-4 h-4 flex items-center justify-center mr-1">
										<i class="ri-add-line"></i>
									</div>
									<span>Добави атрибут</span>
								</button>
							</div>
							<div id="attributes-container" class="space-y-4">
								{% for attribute in product_attribute %}
								<div class="attribute-item border border-gray-200 rounded-lg p-4">
									<div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
										<div>
											<label class="block text-sm font-medium text-gray-700 mb-1">Име на атрибута</label>
											<input type="text" name="product_attribute[{{ loop.index0 }}][name]" class="attribute-name-input w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary text-sm" placeholder="Започнете да пишете за търсене на атрибут..." value="{{ attributes[attribute.attribute_id][active_language_id].name ?? '' }}">
											<div class="attribute-autocomplete-container mt-1 relative" style="z-index: 1000;">
												<!-- Autocomplete suggestions will be populated here -->
											</div>
											<input type="hidden" name="product_attribute[{{ loop.index0 }}][attribute_id]" value="{{ attribute.attribute_id ?? '' }}">
										</div>
										<div class="flex items-end justify-end">
											<button type="button" class="remove-attribute w-8 h-8 flex items-center justify-center bg-red-50 text-red-500 border border-red-200 rounded hover:bg-red-100 transition-colors">
												<i class="ri-delete-bin-line text-sm"></i>
											</button>
										</div>
									</div>
									<!-- Многоезични полета за стойности -->
									<div class="border-t border-gray-200 pt-4">
										<label class="block text-sm font-medium text-gray-700 mb-2">Стойности по езици</label>
										<div class="space-y-3">
											{% for language in languages %}
											<div class="flex items-center space-x-3">
												<div class="w-20 h-6 flex items-center justify-center bg-gray-100 rounded text-xs font-medium text-gray-600">
													{{ language.name }}
												</div>
												<input type="text" name="product_attribute[{{ loop.parent.loop.index0 }}][product_attribute_description][{{ language.language_id }}][text]" class="flex-1 px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary text-sm" placeholder="Стойност на {{ language.name }}" value="{{ attribute.product_attribute_description[language.language_id].text ?? '' }}">
											</div>
											{% endfor %}
										</div>
									</div>
								</div>
								{% else %}
								<div id="no-attributes" class="text-gray-500 text-sm italic text-center py-3 border border-dashed border-gray-300 rounded-lg">
									Няма добавени атрибути. Използвайте бутона "Добави атрибут" за да добавите нов атрибут.
								</div>
								{% endfor %}
							</div>
						</div>

						<!-- Опции на продукта -->
						<div class="border-t border-gray-200 pt-8">
							<div class="mb-4 flex justify-between items-center">
								<h3 class="text-sm font-medium text-gray-700">Опции на продукта</h3>
								<button type="button" id="add-option" class="px-3 py-1.5 bg-primary text-white rounded-button hover:bg-primary/90 transition-colors text-sm flex items-center !rounded-button">
									<div class="w-4 h-4 flex items-center justify-center mr-1">
										<i class="ri-add-line"></i>
									</div>
									<span>Добави опция</span>
								</button>
							</div>
							<div id="options-container" class="space-y-4">
								{% for option in product_option %}
								<div class="option-item border border-gray-200 rounded-lg p-4">
									<div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
										<div>
											<label class="block text-sm font-medium text-gray-700 mb-1">Тип опция</label>
											<select name="product_option[{{ loop.index0 }}][type]" class="option-type-select w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary text-sm" disabled>
												<option value="select" {% if option.type == 'select' %}{{' '}}selected{% endif %}>Избор от списък</option>
												<option value="radio" {% if option.type == 'radio' %}{{' '}}selected{% endif %}>Радио бутони</option>
												<option value="checkbox" {% if option.type == 'checkbox' %}{{' '}}selected{% endif %}>Отметки</option>
												<option value="text" {% if option.type == 'text' %}{{' '}}selected{% endif %}>Текстово поле</option>
												<option value="textarea" {% if option.type == 'textarea' %}{{' '}}selected{% endif %}>Текстова област</option>
												<option value="file" {% if option.type == 'file' %}{{' '}}selected{% endif %}>Файл</option>
												<option value="date" {% if option.type == 'date' %}{{' '}}selected{% endif %}>Дата</option>
												<option value="time" {% if option.type == 'time' %}{{' '}}selected{% endif %}>Време</option>
												<option value="datetime" {% if option.type == 'datetime' %}{{' '}}selected{% endif %}>Дата и време</option>
											</select>
										</div>
										<div>
											<label class="block text-sm font-medium text-gray-700 mb-1">Задължителна</label>
											<div class="flex items-center space-x-2 mt-2">
												<label class="toggle-switch">
													<input type="checkbox" name="product_option[{{ loop.index0 }}][required]" value="1" {% if option.required %}{{' '}}checked{% endif %}>
													<span class="toggle-slider"></span>
												</label>
												<span class="text-sm text-gray-700">Да</span>
											</div>
										</div>
										<div class="flex items-end justify-end">
											<button type="button" class="remove-option w-8 h-8 flex items-center justify-center bg-red-50 text-red-500 border border-red-200 rounded hover:bg-red-100 transition-colors">
												<i class="ri-delete-bin-line text-sm"></i>
											</button>
										</div>
									</div>
									<!-- Опростено поле за име на опцията (само активен език) -->
									<div class="border-t border-gray-200 pt-4 mb-4">
										<label class="block text-sm font-medium text-gray-700 mb-2">Име на опцията</label>
										<input type="text" name="product_option[{{ loop.index0 }}][product_option_description][{{ active_language_id }}][name]" class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary text-sm" placeholder="Въведете име на опцията" value="{{ option.name ?? '' }}" disabled>
										<input type="hidden" name="product_option[{{ loop.index0 }}][option_id]" value="{{ option.option_id ?? '' }}">
									</div>
									<!-- Стойности на опцията (за select, radio, checkbox) -->
									<div class="option-values-section" style="display: {% if option.type in ['select', 'radio', 'checkbox'] %}block{% else %}none{% endif %};">
										<div class="border-t border-gray-200 pt-4">
											<div class="flex justify-between items-center mb-3">
												<label class="block text-sm font-medium text-gray-700">Стойности на опцията</label>
												<button type="button" class="add-option-value px-2 py-1 bg-gray-100 text-gray-700 rounded hover:bg-gray-200 transition-colors text-xs">
													<i class="ri-add-line mr-1"></i>Добави стойност
												</button>
											</div>
											<div class="option-values-container space-y-2">
												{% for value in option.product_option_value %}
												<div class="option-value-item flex items-end gap-3 p-3 bg-gray-50 rounded">
													<div class="flex-1 grid grid-cols-1 md:grid-cols-3 gap-3">
														<div>
															<label class="block text-xs font-medium text-gray-600 mb-1">Стойност</label>
															<select name="product_option[{{ loop.parent.loop.index0 }}][product_option_value][{{ loop.index0 }}][option_value_id]" class="option-value-select w-full px-2 py-1 border border-gray-300 rounded text-xs" data-option-type="{{ option.type }}">
																<option value="">Изберете стойност...</option>
																{% for option_value in option_values if option_value.option_id == option.option_id %}
																	<option value="{{ option_value.option_value_id }}" data-option-id="{{ option_value.option_id }}" {% if value.option_value_id is defined and value.option_value_id == option_value.option_value_id %}{{' '}}selected{% endif %}>{{ option_value.name }}</option>
																{% endfor %}
															</select>
														</div>
														<div>
															<label class="block text-xs font-medium text-gray-600 mb-1">Количество</label>
															<input type="number" name="product_option[{{ loop.parent.loop.index0 }}][product_option_value][{{ loop.index0 }}][quantity]" class="w-full px-2 py-1 border border-gray-300 rounded text-xs" value="{{ value.quantity }}">
														</div>
														<div>
															<label class="block text-xs font-medium text-gray-600 mb-1">Цена</label>
															<input type="number" name="product_option[{{ loop.parent.loop.index0 }}][product_option_value][{{ loop.index0 }}][price]" step="0.01" class="w-full px-2 py-1 border border-gray-300 rounded text-xs" value="{{ value.price|number_format(2, '.', '') }}" onchange="this.value = parseFloat(this.value || 0).toFixed(2)">
														</div>
													</div>
													<div class="flex-shrink-0">
														<button type="button" class="remove-option-value w-6 h-6 flex items-center justify-center bg-red-50 text-red-500 border border-red-200 rounded hover:bg-red-100 transition-colors">
															<i class="ri-close-line text-xs"></i>
														</button>
													</div>
												</div>
												{% endfor %}
											</div>
										</div>
									</div>
								</div>
								{% else %}
								<div id="no-options" class="text-gray-500 text-sm italic text-center py-3 border border-dashed border-gray-300 rounded-lg">
									Няма добавени опции. Използвайте бутона "Добави опция" за да добавите нова опция.
								</div>
								{% endfor %}
							</div>
						</div>
					</div>
					<!-- Related Products Tab (Hidden by default) -->
					<div id="tab-related" class="tab-content hidden">
						<div class="mb-4 flex justify-between items-center">
							<h3 class="text-sm font-medium text-gray-700">Свързани продукти</h3>
							<div class="text-xs text-gray-500">Изберете продукти, които са свързани с този продукт</div>
						</div>
						<div class="mb-4">
							<label class="block text-sm font-medium text-gray-700 mb-2">Търсене на продукти</label>
							<input type="text" name="related_product_search" id="input-related-product" class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary text-sm" placeholder="Започнете да пишете за търсене на продукт...">
							<div id="product-related-autocomplete" class="autocomplete-container hidden">
								<!-- Autocomplete suggestions will be populated here -->
							</div>
						</div>
						<div class="mt-4">
							<h4 class="text-sm font-medium text-gray-700 mb-3">Избрани свързани продукти</h4>
							<div id="product-related-list" class="space-y-2">
								{% for related_product in product_related %}
								<div id="product-related-{{ related_product.product_id }}" class="flex items-center justify-between p-3 bg-gray-100 rounded-lg">
									<div class="flex items-center">
										<div class="w-12 h-12 bg-gray-200 rounded mr-3 flex-shrink-0">
											{% if related_product.thumb %}
											<img src="{{ related_product.thumb }}" alt="{{ related_product.name }}" class="w-full h-full object-cover rounded">
											{% else %}
											<div class="w-full h-full flex items-center justify-center text-gray-400">
												<i class="ri-image-line"></i>
											</div>
											{% endif %}
										</div>
										<div>
											<div class="text-sm font-medium text-gray-900">{{ related_product.name }}</div>
											<div class="text-xs text-gray-500">Код: {{ related_product.model }}</div>
										</div>
									</div>
									<button type="button" class="text-gray-400 hover:text-red-500 remove-related-product" data-product-id="{{ related_product.product_id }}">
										<i class="ri-close-line"></i>
									</button>
									<input type="hidden" name="product_related[]" value="{{ related_product.product_id }}">
								</div>
								{% endfor %}
								{% if product_related is empty %}
								<div id="no-related-products" class="text-gray-500 text-sm italic text-center py-4">
									Няма избрани свързани продукти. Използвайте полето за търсене по-горе за да добавите свързани продукти.
								</div>
								{% endif %}
							</div>
						</div>
					</div>
					<!-- SEO Tab (Hidden by default) -->
					<div id="tab-seo" class="tab-content hidden">
						<div class="space-y-6">
							<div>
								<label class="block text-sm font-medium text-gray-700 mb-1">URL Slug</label>
								<div class="flex">
									<span class="inline-flex items-center px-3 text-gray-500 bg-gray-100 border border-r-0 border-gray-300 rounded-l text-sm">{{ site_url }}</span>
									<input type="text" name="product_seo_url[0][{{ active_language_id }}]" class="flex-1 px-3 py-2 border border-gray-300 rounded-r focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary text-sm" placeholder="product-name" value="{{ product_seo_url[0][active_language_id] ?? '' }}">
								</div>
								<p class="mt-1 text-xs text-gray-500">URL адресът ще бъде генериран автоматично от името на продукта, но можете да го промените.</p>
							</div>
							<div>
								<label class="block text-sm font-medium text-gray-700 mb-1">Meta заглавие</label>
								<input type="text" name="product_description[{{ active_language_id }}][meta_title]" class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary text-sm" placeholder="Въведете мета заглавие" value="{{ product_description[active_language_id].meta_title }}">
								<p class="mt-1 text-xs text-gray-500">Препоръчителна дължина: 50-60 символа</p>
							</div>
							<div>
								<label class="block text-sm font-medium text-gray-700 mb-1">Meta описание</label>
								<textarea name="product_description[{{ active_language_id }}][meta_description]" rows="3" class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary text-sm" placeholder="Въведете мета описание">{{ product_description[active_language_id].meta_description }}</textarea>
								<p class="mt-1 text-xs text-gray-500">Препоръчителна дължина: 150-160 символа</p>
							</div>
							<div>
								<label class="block text-sm font-medium text-gray-700 mb-1">Ключови думи</label>
								<input type="text" name="product_description[{{ active_language_id }}][meta_keyword]" class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary text-sm" placeholder="Въведете ключови думи, разделени със запетая" value="{{ product_description[active_language_id].meta_keyword }}">
								<p class="mt-1 text-xs text-gray-500">Пример: смартфон, телефон, мобилен телефон</p>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</form>
</main>

<script>
	// Set active language ID for JavaScript
	window.activeLanguageId = {{ active_language_id }};
	// Set languages for JavaScript
	window.languages = {{ languages|json_encode|raw }};
	// Set option values for JavaScript
	window.optionValues = {{ option_values|json_encode|raw }};
	// Set all options for JavaScript
	window.allOptions = {{ all_options|json_encode|raw }};
</script>
